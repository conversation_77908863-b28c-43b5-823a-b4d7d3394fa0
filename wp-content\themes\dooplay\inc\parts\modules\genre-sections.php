<?php
/*
* -------------------------------------------------------------------------------------
* @author: Doothemes
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2021 Doothemes. All rights reserved
* -------------------------------------------------------------------------------------
*
* Genre Sections Module
* @since 2.5.0
*
*/

// Check if module is enabled
$genre_sections_enable = dooplay_get_option('genre_sections_enable', true);
if (!$genre_sections_enable) {
    return;
}

// Debug: Check if function exists
if (!function_exists('dooplay_get_option')) {
    echo '<!-- Genre Sections: dooplay_get_option function not found -->';
    return;
}

// Get admin options with safe defaults
$module_title = dooplay_get_option('genre_sections_title', 'Browse by Genre');
$module_subtitle = dooplay_get_option('genre_sections_subtitle', 'Discover content by your favorite genres');
$layout_style = dooplay_get_option('genre_sections_layout', 'grid');
$items_per_section = dooplay_get_option('genre_sections_items_per_section', 8);
$columns = dooplay_get_option('genre_sections_columns', 4);
$show_count = dooplay_get_option('genre_sections_show_count', true);
$show_see_all = dooplay_get_option('genre_sections_show_see_all', true);
$selected_genres = dooplay_get_option('genre_sections_selected', array());
$genre_order = dooplay_get_option('genre_sections_order', 'custom');
$content_types = dooplay_get_option('genre_sections_content_type', array('movies', 'tvshows'));
$sort_content = dooplay_get_option('genre_sections_sort_content', 'date');
$autoplay = dooplay_get_option('genre_sections_autoplay', false);
$autoplay_speed = dooplay_get_option('genre_sections_autoplay_speed', 5000);

// If no genres selected, show a message for testing
if (empty($selected_genres)) {
    echo '<!-- Genre Sections: No genres selected. Please configure in admin panel. -->';
    echo '<div style="padding: 20px; background: #f0f0f0; margin: 20px 0; text-align: center;">';
    echo '<h3>Genre Sections Module</h3>';
    echo '<p>Please go to <strong>WordPress Admin → Appearance → DooPlay Options → Homepage Modules → Genre Sections</strong> to configure this module.</p>';
    echo '<p>Select genres to display and enable the module.</p>';
    echo '</div>';
    return;
}

// Get selected genres
$genre_terms = array();
foreach ($selected_genres as $genre_id) {
    $term = get_term($genre_id, 'genres');
    if ($term && !is_wp_error($term)) {
        $genre_terms[] = $term;
    }
}

if (empty($genre_terms)) {
    return;
}

// Order genres based on settings
switch ($genre_order) {
    case 'name':
        usort($genre_terms, function($a, $b) {
            return strcmp($a->name, $b->name);
        });
        break;
    case 'count':
        usort($genre_terms, function($a, $b) {
            return $b->count - $a->count;
        });
        break;
    case 'random':
        shuffle($genre_terms);
        break;
    // 'custom' keeps the original order from selection
}

// Prepare content query args
$post_types = array();
if (in_array('movies', $content_types)) $post_types[] = 'movies';
if (in_array('tvshows', $content_types)) $post_types[] = 'tvshows';
if (in_array('episodes', $content_types)) $post_types[] = 'episodes';

if (empty($post_types)) {
    return;
}

// Set content ordering
$orderby = 'date';
$order = 'DESC';
switch ($sort_content) {
    case 'title':
        $orderby = 'title';
        $order = 'ASC';
        break;
    case 'rating':
        $orderby = 'meta_value_num';
        break;
    case 'views':
        $orderby = 'meta_value_num';
        break;
    case 'random':
        $orderby = 'rand';
        break;
}

?>

<div class="genre-sections-module">
    <div class="container">
        <?php if ($module_title || $module_subtitle): ?>
        <div class="genre-sections-header">
            <?php if ($module_title): ?>
            <h2 class="genre-sections-title"><?php echo esc_html($module_title); ?></h2>
            <?php endif; ?>
            <?php if ($module_subtitle): ?>
            <p class="genre-sections-subtitle"><?php echo esc_html($module_subtitle); ?></p>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="genre-sections-content layout-<?php echo esc_attr($layout_style); ?>">
            <?php if ($layout_style === 'tabs'): ?>
            <!-- Tabs Layout -->
            <div class="genre-tabs-wrapper">
                <ul class="genre-tabs-nav">
                    <?php foreach ($genre_terms as $index => $term): ?>
                    <li class="genre-tab <?php echo $index === 0 ? 'active' : ''; ?>">
                        <a href="#genre-tab-<?php echo $term->term_id; ?>" data-genre="<?php echo $term->term_id; ?>">
                            <?php echo esc_html($term->name); ?>
                            <?php if ($show_count): ?>
                            <span class="genre-count">(<?php echo $term->count; ?>)</span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>

                <div class="genre-tabs-content">
                    <?php foreach ($genre_terms as $index => $term): ?>
                    <div id="genre-tab-<?php echo $term->term_id; ?>" class="genre-tab-content <?php echo $index === 0 ? 'active' : ''; ?>">
                        <?php
                        // Get content for this genre
                        $query_args = array(
                            'post_type' => $post_types,
                            'posts_per_page' => $items_per_section,
                            'orderby' => $orderby,
                            'order' => $order,
                            'tax_query' => array(
                                array(
                                    'taxonomy' => 'genres',
                                    'field' => 'term_id',
                                    'terms' => $term->term_id,
                                ),
                            ),
                        );

                        if ($sort_content === 'rating') {
                            $query_args['meta_key'] = 'dt_rating_average';
                        } elseif ($sort_content === 'views') {
                            $query_args['meta_key'] = 'dt_views_count';
                        }

                        $genre_query = new WP_Query($query_args);
                        ?>

                        <?php if ($genre_query->have_posts()): ?>
                        <div class="genre-content-grid columns-<?php echo $columns; ?>">
                            <?php while ($genre_query->have_posts()): $genre_query->the_post(); ?>
                                <?php get_template_part('inc/parts/item'); ?>
                            <?php endwhile; ?>
                        </div>

                        <?php if ($show_see_all): ?>
                        <div class="genre-see-all">
                            <a href="<?php echo esc_url(get_term_link($term)); ?>" class="btn-see-all">
                                <?php _d('See all'); ?> <?php echo esc_html($term->name); ?>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>

                        <?php wp_reset_postdata(); ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <?php else: ?>
            <!-- Grid or Carousel Layout -->
            <?php foreach ($genre_terms as $term): ?>
            <div class="genre-section" data-genre="<?php echo $term->term_id; ?>">
                <div class="genre-section-header">
                    <h3 class="genre-section-title">
                        <a href="<?php echo esc_url(get_term_link($term)); ?>">
                            <?php echo esc_html($term->name); ?>
                        </a>
                        <?php if ($show_count): ?>
                        <span class="genre-count">(<?php echo $term->count; ?>)</span>
                        <?php endif; ?>
                    </h3>
                    <?php if ($show_see_all): ?>
                    <a href="<?php echo esc_url(get_term_link($term)); ?>" class="genre-see-all">
                        <?php _d('See all'); ?> <i class="fas fa-arrow-right"></i>
                    </a>
                    <?php endif; ?>
                </div>

                <?php
                // Get content for this genre
                $query_args = array(
                    'post_type' => $post_types,
                    'posts_per_page' => $items_per_section,
                    'orderby' => $orderby,
                    'order' => $order,
                    'tax_query' => array(
                        array(
                            'taxonomy' => 'genres',
                            'field' => 'term_id',
                            'terms' => $term->term_id,
                        ),
                    ),
                );

                if ($sort_content === 'rating') {
                    $query_args['meta_key'] = 'dt_rating_average';
                } elseif ($sort_content === 'views') {
                    $query_args['meta_key'] = 'dt_views_count';
                }

                $genre_query = new WP_Query($query_args);
                ?>

                <?php if ($genre_query->have_posts()): ?>
                <div class="genre-content-wrapper">
                    <?php if ($layout_style === 'carousel'): ?>
                    <div class="genre-carousel" id="genre-carousel-<?php echo $term->term_id; ?>">
                        <?php while ($genre_query->have_posts()): $genre_query->the_post(); ?>
                            <div class="carousel-item">
                                <?php get_template_part('inc/parts/item'); ?>
                            </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="genre-content-grid columns-<?php echo $columns; ?>">
                        <?php while ($genre_query->have_posts()): $genre_query->the_post(); ?>
                            <?php get_template_part('inc/parts/item'); ?>
                        <?php endwhile; ?>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <?php wp_reset_postdata(); ?>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>
